<!-- license-dashboard.component.html -->
<div class="container mt-4">
    <h2>Owner Dashboard - Salon Yönetim Paneli</h2>

    <div *ngIf="isLoading" class="d-flex justify-content-center my-5">
      <app-loading-spinner></app-loading-spinner>
    </div>

    <div *ngIf="!isLoading" class="dashboard-content">
      <!-- Statistics Cards -->
      <div class="row mt-4">
        <div class="col-md-4">
          <div class="card bg-primary text-white mb-4">
            <div class="card-body">
              <div class="d-flex justify-content-between align-items-center">
                <div>
                  <h5 class="card-title mb-0">Toplam Admin</h5>
                  <h2 class="mt-2 mb-0">{{ totalAdmins }}</h2>
                  <small>Salon Sahipleri</small>
                </div>
                <i class="fas fa-users fa-3x opacity-50"></i>
              </div>
            </div>
          </div>
        </div>

        <div class="col-md-4">
          <div class="card bg-success text-white mb-4">
            <div class="card-body">
              <div class="d-flex justify-content-between align-items-center">
                <div>
                  <h5 class="card-title mb-0">Aktif Lisanslar</h5>
                  <h2 class="mt-2 mb-0">{{ totalActiveLicenses }}</h2>
                  <small>Lisansı Aktif Salonlar</small>
                </div>
                <i class="fas fa-certificate fa-3x opacity-50"></i>
              </div>
            </div>
          </div>
        </div>

        <div class="col-md-4">
          <div class="card bg-info text-white mb-4">
            <div class="card-body">
              <div class="d-flex justify-content-between align-items-center">
                <div>
                  <h5 class="card-title mb-0">Bu Ay Gelir</h5>
                  <h2 class="mt-2 mb-0">{{ monthlyRevenue | currency:'TRY':'symbol':'1.0-0' }}</h2>
                  <small>Lisans Ödemeleri</small>
                </div>
                <i class="fas fa-money-bill-wave fa-3x opacity-50"></i>
              </div>
            </div>
          </div>
        </div>
      </div>
  
      <!-- Admin (Salon Sahipleri) Bilgileri -->
      <div class="row mt-4">
        <div class="col-md-12">
          <div class="card">
            <div class="card-header">
              <h4>Salon Sahipleri - Lisans Durumuna Göre Sıralı</h4>
              <small class="text-muted">En kritik durumda olanlar üstte gösterilir</small>
            </div>
            <div class="card-body">
              <div *ngIf="allAdmins.length === 0" class="alert alert-info">
                Henüz kayıtlı salon sahibi bulunmamaktadır.
              </div>

              <div *ngIf="allAdmins.length > 0" class="table-responsive">
                <table class="table table-hover">
                  <thead class="table-dark">
                    <tr>
                      <th>Salon Sahibi</th>
                      <th>Salon Adı</th>
                      <th>Şehir/İlçe</th>
                      <th>İletişim</th>
                      <th>Kalan Gün</th>
                      <th>Lisans Durumu</th>
                    </tr>
                  </thead>
                  <tbody>
                    <tr *ngFor="let admin of allAdmins"
                        [class]="(admin.remainingDays || 0) <= 0 ? 'table-danger' :
                                (admin.remainingDays || 0) <= 7 ? 'table-warning' :
                                (admin.remainingDays || 0) <= 30 ? 'table-info' : ''">
                      <td>
                        <strong>{{ admin.companyUserName }}</strong><br>
                        <small class="text-muted">{{ admin.companyUserEmail }}</small>
                      </td>
                      <td>
                        <strong>{{ admin.companyName }}</strong><br>
                        <small class="text-muted">{{ admin.companyAdress }}</small>
                      </td>
                      <td>
                        {{ admin.cityName }}<br>
                        <small>{{ admin.townName }}</small>
                      </td>
                      <td>
                        <i class="fas fa-phone"></i> {{ admin.companyUserPhoneNumber }}
                      </td>
                      <td>
                        <span [class]="(admin.remainingDays || 0) <= 0 ? 'badge bg-danger' :
                                      (admin.remainingDays || 0) <= 7 ? 'badge bg-warning' :
                                      (admin.remainingDays || 0) <= 30 ? 'badge bg-info' : 'badge bg-success'">
                          {{ (admin.remainingDays === -999 || !admin.remainingDays) ? 'Lisans Yok' : (admin.remainingDays + ' gün') }}
                        </span>
                      </td>
                      <td>
                        <span [class]="(admin.remainingDays || 0) <= 0 ? 'badge bg-danger' :
                                      (admin.remainingDays || 0) <= 7 ? 'badge bg-warning' :
                                      (admin.remainingDays || 0) <= 30 ? 'badge bg-info' : 'badge bg-success'">
                          {{ admin.licenseStatus || 'Bilinmiyor' }}
                        </span>
                      </td>
                    </tr>
                  </tbody>
                </table>
              </div>
            </div>
          </div>
        </div>
      </div>
  
      <!-- Sistem Durumu ve Gelir Bilgileri -->
      <div class="row mt-4">
        <div class="col-md-6">
          <div class="card">
            <div class="card-header">
              <h4>📊 Sistem Durumu</h4>
            </div>
            <div class="card-body">
              <!-- Genel Durum Özeti -->
              <div class="row mb-3">
                <div class="col-4">
                  <div class="text-center p-2 bg-primary text-white rounded">
                    <h6>Toplam Salon</h6>
                    <h4>{{ totalAdmins }}</h4>
                  </div>
                </div>
                <div class="col-4">
                  <div class="text-center p-2 bg-success text-white rounded">
                    <h6>Aktif Lisans</h6>
                    <h4>{{ totalActiveLicenses }}</h4>
                  </div>
                </div>
                <div class="col-4">
                  <div class="text-center p-2 bg-danger text-white rounded">
                    <h6>Pasif/Yok</h6>
                    <h4>{{ inactiveAdmins }}</h4>
                  </div>
                </div>
              </div>

              <!-- Sistem Durumu -->
              <div class="alert alert-info">
                <i class="fas fa-info-circle"></i> <strong>Sistem Durumu</strong>
                <br><small>Dashboard başarıyla yüklendi</small>
              </div>

              <!-- Hızlı Aksiyon Butonları -->
              <div class="d-grid gap-2">
                <button class="btn btn-outline-primary btn-sm" (click)="loadDashboardData()">
                  <i class="fas fa-sync-alt"></i> Verileri Yenile
                </button>
              </div>
            </div>
          </div>
        </div>

        <div class="col-md-6">
          <div class="card">
            <div class="card-header">
              <h4>💰 Gelir Bilgileri</h4>
            </div>
            <div class="card-body">
              <div class="row">
                <div class="col-6">
                  <div class="text-center p-3 border rounded">
                    <h6 class="text-muted">Bu Ay</h6>
                    <h3 class="text-success">{{ monthlyRevenue | currency:'TRY':'symbol':'1.0-0' }}</h3>
                  </div>
                </div>
                <div class="col-6">
                  <div class="text-center p-3 border rounded">
                    <h6 class="text-muted">Toplam</h6>
                    <h3 class="text-primary">{{ totalRevenue | currency:'TRY':'symbol':'1.0-0' }}</h3>
                  </div>
                </div>
              </div>

              <hr>
              <h6>Son 5 İşlem</h6>
              <div *ngIf="recentTransactions.length === 0" class="text-muted text-center py-3">
                <i class="fas fa-info-circle"></i> İşlem bulunmamaktadır.
              </div>

              <div *ngIf="recentTransactions.length > 0" class="table-responsive">
                <table class="table table-sm table-hover">
                  <thead>
                    <tr>
                      <th>Tarih</th>
                      <th>Üye</th>
                      <th>Tutar</th>
                      <th>Ödeme</th>
                    </tr>
                  </thead>
                  <tbody>
                    <tr *ngFor="let transaction of recentTransactions">
                      <td><small>{{ transaction.transactionDate | date:'dd/MM' }}</small></td>
                      <td>
                        <small class="fw-bold">{{ transaction.userName || 'Bilinmiyor' }}</small>
                        <br>
                        <small class="text-muted">{{ transaction.companyName || '' }}</small>
                      </td>
                      <td class="text-success fw-bold">{{ transaction.amount | currency:'TRY':'symbol':'1.0-0' }}</td>
                      <td><small class="text-muted">{{ transaction.paymentMethod }}</small></td>
                    </tr>
                  </tbody>
                </table>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>