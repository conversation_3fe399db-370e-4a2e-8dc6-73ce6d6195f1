<!-- license-dashboard.component.html -->
<div class="container mt-4">
    <h2>Owner Dashboard - Salon Yönetim Paneli</h2>

    <div *ngIf="isLoading" class="d-flex justify-content-center my-5">
      <app-loading-spinner></app-loading-spinner>
    </div>

    <div *ngIf="!isLoading" class="dashboard-content">
      <!-- Statistics Cards -->
      <div class="row mt-4">
        <div class="col-md-4">
          <div class="card bg-primary text-white mb-4">
            <div class="card-body">
              <div class="d-flex justify-content-between align-items-center">
                <div>
                  <h5 class="card-title mb-0">Toplam Admin</h5>
                  <h2 class="mt-2 mb-0">{{ totalAdmins }}</h2>
                  <small>Salon Sahipleri</small>
                </div>
                <i class="fas fa-users fa-3x opacity-50"></i>
              </div>
            </div>
          </div>
        </div>

        <div class="col-md-4">
          <div class="card bg-success text-white mb-4">
            <div class="card-body">
              <div class="d-flex justify-content-between align-items-center">
                <div>
                  <h5 class="card-title mb-0">Aktif Lisanslar</h5>
                  <h2 class="mt-2 mb-0">{{ totalActiveLicenses }}</h2>
                  <small>Lisansı Aktif Salonlar</small>
                </div>
                <i class="fas fa-certificate fa-3x opacity-50"></i>
              </div>
            </div>
          </div>
        </div>

        <div class="col-md-4">
          <div class="card bg-info text-white mb-4">
            <div class="card-body">
              <div class="d-flex justify-content-between align-items-center">
                <div>
                  <h5 class="card-title mb-0">Bu Ay Gelir</h5>
                  <h2 class="mt-2 mb-0">{{ monthlyRevenue | currency:'TRY':'symbol':'1.0-0' }}</h2>
                  <small>Lisans Ödemeleri</small>
                </div>
                <i class="fas fa-money-bill-wave fa-3x opacity-50"></i>
              </div>
            </div>
          </div>
        </div>
      </div>
  
      <!-- Admin (Salon Sahipleri) Bilgileri -->
      <div class="row mt-4">
        <div class="col-md-12">
          <div class="card">
            <div class="card-header">
              <h4>Salon Sahipleri - Lisans Durumuna Göre Sıralı</h4>
              <small class="text-muted">En kritik durumda olanlar üstte gösterilir</small>
            </div>
            <div class="card-body">
              <div *ngIf="allAdmins.length === 0" class="alert alert-info">
                Henüz kayıtlı salon sahibi bulunmamaktadır.
              </div>

              <div *ngIf="allAdmins.length > 0" class="table-responsive">
                <table class="table table-hover">
                  <thead class="table-dark">
                    <tr>
                      <th>Salon Sahibi</th>
                      <th>Salon Adı</th>
                      <th>Şehir/İlçe</th>
                      <th>İletişim</th>
                      <th>Kalan Gün</th>
                      <th>Lisans Durumu</th>
                    </tr>
                  </thead>
                  <tbody>
                    <tr *ngFor="let admin of allAdmins"
                        [class]="(admin.remainingDays || 0) <= 0 ? 'table-danger' :
                                (admin.remainingDays || 0) <= 7 ? 'table-warning' :
                                (admin.remainingDays || 0) <= 30 ? 'table-info' : ''">
                      <td>
                        <strong>{{ admin.companyUserName }}</strong><br>
                        <small class="text-muted">{{ admin.companyUserEmail }}</small>
                      </td>
                      <td>
                        <strong>{{ admin.companyName }}</strong><br>
                        <small class="text-muted">{{ admin.companyAdress }}</small>
                      </td>
                      <td>
                        {{ admin.cityName }}<br>
                        <small>{{ admin.townName }}</small>
                      </td>
                      <td>
                        <i class="fas fa-phone"></i> {{ admin.companyUserPhoneNumber }}
                      </td>
                      <td>
                        <span [class]="(admin.remainingDays || 0) <= 0 ? 'badge bg-danger' :
                                      (admin.remainingDays || 0) <= 7 ? 'badge bg-warning' :
                                      (admin.remainingDays || 0) <= 30 ? 'badge bg-info' : 'badge bg-success'">
                          {{ (admin.remainingDays === -999 || !admin.remainingDays) ? 'Lisans Yok' : (admin.remainingDays + ' gün') }}
                        </span>
                      </td>
                      <td>
                        <span [class]="(admin.remainingDays || 0) <= 0 ? 'badge bg-danger' :
                                      (admin.remainingDays || 0) <= 7 ? 'badge bg-warning' :
                                      (admin.remainingDays || 0) <= 30 ? 'badge bg-info' : 'badge bg-success'">
                          {{ admin.licenseStatus || 'Bilinmiyor' }}
                        </span>
                      </td>
                    </tr>
                  </tbody>
                </table>
              </div>
            </div>
          </div>
        </div>
      </div>
  
      <!-- Sistem Durumu ve Gelir Bilgileri -->
      <div class="row mt-4">
        <div class="col-md-4">
          <div class="card">
            <div class="card-header">
              <h5>📊 Sistem Durumu</h5>
            </div>
            <div class="card-body">
              <!-- Genel Durum Özeti -->
              <div class="row mb-3">
                <div class="col-4">
                  <div class="text-center p-2 bg-primary text-white rounded">
                    <h6>Toplam Salon</h6>
                    <h4>{{ totalAdmins }}</h4>
                  </div>
                </div>
                <div class="col-4">
                  <div class="text-center p-2 bg-success text-white rounded">
                    <h6>Aktif Lisans</h6>
                    <h4>{{ totalActiveLicenses }}</h4>
                  </div>
                </div>
                <div class="col-4">
                  <div class="text-center p-2 bg-danger text-white rounded">
                    <h6>Pasif/Yok</h6>
                    <h4>{{ inactiveAdmins }}</h4>
                  </div>
                </div>
              </div>




            </div>
          </div>
        </div>

        <div class="col-md-8">
          <div class="card">
            <div class="card-header">
              <h4>💰 Gelir Bilgileri ve İşlem Geçmişi</h4>
            </div>
            <div class="card-body">
              <!-- Gelir Özeti -->
              <div class="row mb-4">
                <div class="col-md-3">
                  <div class="text-center p-3 bg-success text-white rounded">
                    <h6>Bu Ay Gelir</h6>
                    <h4>{{ monthlyRevenue | currency:'TRY':'symbol':'1.0-0' }}</h4>
                    <small>Lisans Ödemeleri</small>
                  </div>
                </div>
                <div class="col-md-3">
                  <div class="text-center p-3 bg-primary text-white rounded">
                    <h6>Toplam Gelir</h6>
                    <h4>{{ totalRevenue | currency:'TRY':'symbol':'1.0-0' }}</h4>
                    <small>Tüm Zamanlar</small>
                  </div>
                </div>
                <div class="col-md-3">
                  <div class="text-center p-3 bg-info text-white rounded">
                    <h6>Ortalama İşlem</h6>
                    <h4>{{ getAverageTransaction() | currency:'TRY':'symbol':'1.0-0' }}</h4>
                    <small>İşlem Başına</small>
                  </div>
                </div>
                <div class="col-md-3">
                  <div class="text-center p-3 bg-warning text-white rounded">
                    <h6>Toplam İşlem</h6>
                    <h4>{{ recentTransactions.length }}</h4>
                    <small>Bu Ay</small>
                  </div>
                </div>
              </div>

              <hr>
              <h5><i class="fas fa-history"></i> Son İşlemler</h5>
              <div *ngIf="recentTransactions.length === 0" class="text-muted text-center py-3">
                <i class="fas fa-info-circle"></i> İşlem bulunmamaktadır.
              </div>

              <div *ngIf="recentTransactions.length > 0" class="table-responsive">
                <table class="table table-hover">
                  <thead class="table-dark">
                    <tr>
                      <th><i class="fas fa-calendar"></i> Tarih</th>
                      <th><i class="fas fa-user"></i> Müşteri Bilgileri</th>
                      <th><i class="fas fa-box"></i> Paket</th>
                      <th><i class="fas fa-money-bill"></i> Tutar</th>
                      <th><i class="fas fa-credit-card"></i> Ödeme</th>
                      <th><i class="fas fa-info-circle"></i> Durum</th>
                    </tr>
                  </thead>
                  <tbody>
                    <tr *ngFor="let transaction of recentTransactions">
                      <td>
                        <strong>{{ transaction.transactionDate | date:'dd/MM/yyyy' }}</strong>
                        <br>
                        <small class="text-muted">{{ transaction.transactionDate | date:'HH:mm' }}</small>
                      </td>
                      <td>
                        <div class="fw-bold text-primary">{{ transaction.userName || 'Bilinmiyor' }}</div>
                        <small class="text-muted">{{ transaction.companyName || 'Şirket Belirtilmemiş' }}</small>
                      </td>
                      <td>
                        <span class="badge bg-info">Paket #{{ transaction.licensePackageID }}</span>
                      </td>
                      <td>
                        <span class="text-success fw-bold fs-5">{{ transaction.amount | currency:'TRY':'symbol':'1.0-0' }}</span>
                      </td>
                      <td>
                        <span class="badge bg-secondary">{{ transaction.paymentMethod || 'Kredi Kartı' }}</span>
                      </td>
                      <td>
                        <span class="badge bg-success">
                          <i class="fas fa-check"></i> Başarılı
                        </span>
                      </td>
                    </tr>
                  </tbody>
                </table>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>