// license-dashboard.component.ts
import { Component, OnInit } from '@angular/core';
import { ToastrService } from 'ngx-toastr';
import { LicensePackageService } from '../../services/license-package.service';
import { UserLicenseService } from '../../services/user-license.service';
import { LicenseTransactionService } from '../../services/license-transaction.service';
import { CompanyUserDetailService } from '../../services/company-user-detail.service';
import { AuthService } from '../../services/auth.service';
import { UserModel } from '../../models/userModel';
import { LicenseTransaction } from '../../models/LicenseTransaction';
import { UserLicenseDto } from '../../models/UserLicenseDto';
import { CompanyDetail } from '../../models/companyDetail';

@Component({
  selector: 'app-license-dashboard',
  templateUrl: './license-dashboard.component.html',
  styleUrls: ['./license-dashboard.component.css'],
  standalone:false
})
export class LicenseDashboardComponent implements OnInit {
  isLoading = false;
  currentUser: UserModel | null = null;

  // Salon (Admin) Bilgileri
  allAdmins: CompanyDetail[] = [];
  totalAdmins = 0;
  activeAdmins = 0;
  inactiveAdmins = 0;
  newAdminsThisMonth = 0;

  // Lisans Bilgileri
  userLicenses: UserLicenseDto[] = [];
  totalActiveLicenses = 0;

  // Gelir Bilgileri
  recentTransactions: LicenseTransaction[] = [];
  totalRevenue = 0;
  monthlyRevenue = 0;

  // Computed properties
  get hasInactiveAdmins(): boolean {
    return this.inactiveAdmins > 0;
  }
  
  constructor(
    private licensePackageService: LicensePackageService,
    private userLicenseService: UserLicenseService,
    private licenseTransactionService: LicenseTransactionService,
    private companyUserDetailService: CompanyUserDetailService,
    private authService: AuthService,
    private toastr: ToastrService
  ) {}

  ngOnInit(): void {
    this.currentUser = this.authService.currentUserValue;
    this.loadDashboardData();
  }

  loadDashboardData(): void {
    this.isLoading = true;

    // Önce lisans bilgilerini yükle, sonra admin bilgilerini zenginleştir
    this.loadLicenseData();

    // Revenue data'yı en son yükle ki user bilgileri mevcut olsun
    setTimeout(() => this.loadRevenueData(), 200);
  }

  loadAdminData(): void {
    this.companyUserDetailService.getCompanyUserDetails().subscribe({
      next: (response) => {
        if (response.success) {
          this.allAdmins = response.data;
          this.totalAdmins = this.allAdmins.length;

          // Admin'lerin lisans bilgilerini ekle ve sırala
          this.enrichAdminsWithLicenseInfo();

          // Lisans durumuna göre doğru hesaplama yap
          this.calculateCorrectLicenseStats();

          // Bu ay eklenen adminler (CreationDate yoksa yaklaşık hesap)
          const thisMonth = new Date();
          thisMonth.setDate(1);
          this.newAdminsThisMonth = this.allAdmins.length; // Gerçek tarih bilgisi olmadığı için placeholder
        }
      },
      error: (error) => {
        this.toastr.error('Admin bilgileri yüklenirken bir hata oluştu', 'Hata');
      }
    });
  }

  enrichAdminsWithLicenseInfo(): void {
    // Her admin için lisans bilgisini bul ve ekle
    this.allAdmins.forEach(admin => {
      const adminLicense = this.userLicenses.find(license =>
        license.companyName === admin.companyName ||
        license.userName === admin.companyUserName
      );

      if (adminLicense) {
        (admin as any).remainingDays = adminLicense.remainingDays;
        (admin as any).licenseStatus = adminLicense.remainingDays > 0 ? 'Aktif' : 'Süresi Dolmuş';
      } else {
        (admin as any).remainingDays = -999; // Lisansı yok
        (admin as any).licenseStatus = 'Lisans Yok';
      }
    });

    // En az günü olana göre sırala (en kritik durumda olanlar üstte)
    this.allAdmins.sort((a: any, b: any) => a.remainingDays - b.remainingDays);
  }

  calculateCorrectLicenseStats(): void {
    // Adminlerin lisans durumlarına göre doğru hesaplama yap
    const adminsWithActiveLicense = this.allAdmins.filter(admin =>
      (admin as any).remainingDays > 0
    );

    const adminsWithExpiredLicense = this.allAdmins.filter(admin =>
      (admin as any).remainingDays <= 0 && (admin as any).remainingDays !== -999
    );

    const adminsWithoutLicense = this.allAdmins.filter(admin =>
      (admin as any).remainingDays === -999
    );

    // Doğru sayıları ata
    this.totalActiveLicenses = adminsWithActiveLicense.length;
    this.activeAdmins = adminsWithActiveLicense.length;
    this.inactiveAdmins = adminsWithExpiredLicense.length + adminsWithoutLicense.length;

    console.log('Lisans İstatistikleri:', {
      totalAdmins: this.totalAdmins,
      activeLicenses: this.totalActiveLicenses,
      inactiveAdmins: this.inactiveAdmins,
      withoutLicense: adminsWithoutLicense.length
    });
  }

  loadLicenseData(): void {
    this.userLicenseService.getAll().subscribe({
      next: (response) => {
        if (response.success) {
          this.userLicenses = response.data;
          // Aktif lisans sayısını hesapla (remainingDays > 0 olanlar)
          this.totalActiveLicenses = this.userLicenses.filter(l => l.remainingDays > 0).length;

          // Lisans bilgileri yüklendikten sonra admin bilgilerini yükle
          this.loadAdminData();
        }
        this.isLoading = false;
      },
      error: (error) => {
        this.toastr.error('Lisans bilgileri yüklenirken bir hata oluştu', 'Hata');
        this.isLoading = false;
      }
    });
  }

  loadRevenueData(): void {
    this.licenseTransactionService.getAll().subscribe({
      next: (response) => {
        if (response.success) {
          // Sort by date descending and take first 5
          this.recentTransactions = response.data
            .sort((a, b) => new Date(b.transactionDate).getTime() - new Date(a.transactionDate).getTime())
            .slice(0, 5);

          // Her transaction için user bilgilerini ekle
          this.enrichTransactionsWithUserInfo();

          // Calculate total revenue from all transactions
          this.totalRevenue = response.data.reduce((sum, transaction) => sum + transaction.amount, 0);

          // Calculate monthly revenue (current month)
          const currentMonth = new Date().getMonth();
          const currentYear = new Date().getFullYear();
          this.monthlyRevenue = response.data
            .filter(t => {
              const transactionDate = new Date(t.transactionDate);
              return transactionDate.getMonth() === currentMonth && transactionDate.getFullYear() === currentYear;
            })
            .reduce((sum, transaction) => sum + transaction.amount, 0);
        }
      },
      error: (error) => {
        this.toastr.error('İşlem bilgileri yüklenirken bir hata oluştu', 'Hata');
      }
    });
  }

  enrichTransactionsWithUserInfo(): void {
    // Her transaction için userLicenses'dan user bilgilerini bul ve ekle
    this.recentTransactions.forEach(transaction => {
      const userLicense = this.userLicenses.find(license =>
        license.userID === transaction.userID
      );

      if (userLicense) {
        (transaction as any).userName = userLicense.userName;
        (transaction as any).companyName = userLicense.companyName;
      } else {
        (transaction as any).userName = 'Bilinmiyor';
        (transaction as any).companyName = '';
      }
    });
  }

  getUserLicenses(): void {
    if (this.currentUser) {
      this.userLicenseService.getActiveByUserId(parseInt(this.currentUser.nameidentifier)).subscribe({
        next: (response) => {
          this.userLicenses = response.data;
        },
        error: (error) => {
          this.toastr.error('Lisans bilgileri yüklenirken bir hata oluştu', 'Hata');
        }
      });
    }
  }
  
  getRemainingDaysClass(days: number): string {
    if (days <= 0) {
      return 'text-danger';
    } else if (days <= 7) {
      return 'text-warning';
    } else {
      return 'text-success';
    }
  }

  getBadgeClass(days: number): string {
    if (days <= 0) {
      return 'badge bg-danger';
    } else if (days <= 7) {
      return 'badge bg-warning';
    } else {
      return 'badge bg-success';
    }
  }

  getLicenseStatus(days: number): string {
    return days > 0 ? 'Aktif' : 'Süresi Dolmuş';
  }
}