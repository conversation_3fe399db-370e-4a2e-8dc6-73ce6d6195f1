
export default {
  bootstrap: () => import('./main.server.mjs').then(m => m.default),
  inlineCriticalCss: true,
  baseHref: '/',
  locale: undefined,
  routes: undefined,
  entryPointToBrowserMapping: {},
  assets: {
    'index.csr.html': {size: 41548, hash: 'ca2d595f8061bef93d56efe8f9a5387c5f4c0b592662899292255851fe65aaab', text: () => import('./assets-chunks/index_csr_html.mjs').then(m => m.default)},
    'index.server.html': {size: 21716, hash: '3a1d8d55a87255731713c9bb61420131515d15c1f4d245f24ca3c5125e24e8c6', text: () => import('./assets-chunks/index_server_html.mjs').then(m => m.default)},
    'styles-TBXNKKXU.css': {size: 298105, hash: '7i4JWBKmeRM', text: () => import('./assets-chunks/styles-TBXNKKXU_css.mjs').then(m => m.default)}
  },
};
