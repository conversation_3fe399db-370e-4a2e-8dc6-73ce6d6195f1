import { Component, OnInit, Output, EventEmitter, ViewChild, Optional, Inject } from '@angular/core';
import { FormBuilder, FormGroup, Validators } from '@angular/forms';
import { MatDialogRef, MAT_DIALOG_DATA } from '@angular/material/dialog';
import { ToastrService } from 'ngx-toastr';
import { LicensePackageService } from '../../../services/license-package.service';
import { LicensePackage } from '../../../models/licensePackage';
import { LicensePackagesListComponent } from '../../license-packages-list/license-packages-list.component';

@Component({
  selector: 'app-license-package-add',
  templateUrl: './license-package-add.component.html',
  styleUrls: ['./license-package-add.component.css'],
  standalone: false
})
export class LicensePackageAddComponent implements OnInit {
  licensePackageAddForm: FormGroup;
  isSubmitting = false;

  @Output() licensePackageAdded = new EventEmitter<void>();
  @ViewChild('licensePackageList') licensePackageList: LicensePackagesListComponent;

  constructor(
    private formBuilder: FormBuilder,
    private licensePackageService: LicensePackageService,
    private toastrService: ToastrService,
    @Optional() private dialogRef: MatDialogRef<LicensePackageAddComponent>,
    @Optional() @Inject(MAT_DIALOG_DATA) public data: any
  ) {}

  ngOnInit(): void {
    this.createLicensePackageAddForm();
  }

  createLicensePackageAddForm() {
    this.licensePackageAddForm = this.formBuilder.group({
      name: ['', Validators.required],
      description: ['', Validators.required],
      role: ['admin', Validators.required],
      year: ['0', Validators.required],
      month: ['1', Validators.required],
      day: ['0', Validators.required],
      price: ['', [Validators.required, Validators.min(0)]],
      isActive: [true]
    });
  }

  add() {
    if (this.licensePackageAddForm.valid) {
      this.isSubmitting = true;
      let licensePackageModel = Object.assign(
        {},
        this.licensePackageAddForm.value
      );

      // Calculate total days from year, month, day
      const totalDays =
        parseInt(licensePackageModel.year) * 365 +
        parseInt(licensePackageModel.month) * 30 +
        parseInt(licensePackageModel.day);

      licensePackageModel.durationDays = totalDays;

      // Remove year, month, day from the model
      delete licensePackageModel.year;
      delete licensePackageModel.month;
      delete licensePackageModel.day;

      // Set creation date
      licensePackageModel.creationDate = new Date();

      this.licensePackageService.add(licensePackageModel).subscribe({
        next: (response) => {
          this.isSubmitting = false;
          this.toastrService.success(response.message, 'Başarılı');

          // Dialog olarak kullanılıyorsa dialog'u kapat
          if (this.dialogRef) {
            this.dialogRef.close(true);
          } else {
            // Normal component olarak kullanılıyorsa form'u resetle ve listeyi güncelle
            this.resetForm();
            this.licensePackageAdded.emit();
            // Lisans paketleri listesini güncelle
            if (this.licensePackageList) {
              this.licensePackageList.loadLicensePackages();
            }
          }
        },
        error: (error) => {
          this.isSubmitting = false;
          this.toastrService.error('Lisans paketi eklenirken bir hata oluştu', 'Hata');
        }
      });
    } else {
      this.toastrService.error('Lütfen tüm alanları doğru şekilde doldurun', 'Hata');
    }
  }

  resetForm() {
    this.licensePackageAddForm.reset({
      name: '',
      description: '',
      role: 'admin',
      year: '0',
      month: '1',
      day: '0',
      price: '',
      isActive: true
    });
  }
}
