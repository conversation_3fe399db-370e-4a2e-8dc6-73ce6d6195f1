﻿using Core.Utilities.Results;
using Entities.Concrete;

namespace Business.Abstract
{
    public interface ILicenseTransactionService
    {
        IDataResult<List<LicenseTransaction>> GetAll();
        IDataResult<List<LicenseTransaction>> GetByUserId(int userId);
        IResult Add(LicenseTransaction licenseTransaction);
        IResult Delete(int id);
        IDataResult<object> GetTotals();
        IDataResult<object> GetMonthlyRevenue(int year);
    }
}
