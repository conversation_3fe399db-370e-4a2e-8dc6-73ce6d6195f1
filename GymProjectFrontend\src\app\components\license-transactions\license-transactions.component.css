/* Modern License Transactions Styles - Based on Payment History */

/* Root Variables */
:root {
  --primary: #007bff;
  --primary-light: rgba(0, 123, 255, 0.1);
  --success: #28a745;
  --success-light: rgba(40, 167, 69, 0.1);
  --warning: #ffc107;
  --warning-light: rgba(255, 193, 7, 0.1);
  --danger: #dc3545;
  --danger-light: rgba(220, 53, 69, 0.1);
  --info: #17a2b8;
  --info-light: rgba(23, 162, 184, 0.1);
  --light: #f8f9fa;
  --dark: #343a40;
  --text-primary: #212529;
  --text-secondary: #6c757d;
  --border-color: #dee2e6;
  --border-radius: 0.375rem;
  --border-radius-lg: 0.5rem;
  --border-radius-md: 0.375rem;
  --shadow-sm: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
  --shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);
  --shadow-lg: 0 1rem 3rem rgba(0, 0, 0, 0.175);
}

/* Base Styles */
.container-fluid {
  padding: 1.5rem;
}

.fade-in {
  animation: fadeIn 0.5s ease-in;
}

@keyframes fadeIn {
  from { opacity: 0; transform: translateY(20px); }
  to { opacity: 1; transform: translateY(0); }
}

.content-blur {
  filter: blur(2px);
  pointer-events: none;
}

/* Total Cards */
.total-card {
  background: linear-gradient(135deg, var(--light) 0%, #ffffff 100%);
  border: 1px solid var(--border-color);
  border-radius: var(--border-radius-lg);
  padding: 1.5rem;
  display: flex;
  align-items: center;
  transition: all 0.3s ease;
  box-shadow: var(--shadow-sm);
  position: relative;
  overflow: hidden;
  animation: slideIn 0.6s ease-out;
}

.total-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
  background: linear-gradient(90deg, var(--primary), var(--info));
  border-radius: var(--border-radius-lg) var(--border-radius-lg) 0 0;
}

.total-card:hover {
  transform: translateY(-2px);
  box-shadow: var(--shadow);
}

.total-cash {
  background: linear-gradient(135deg, var(--success-light) 0%, #ffffff 100%);
}

.total-cash::before {
  background: linear-gradient(90deg, var(--success), #20c997);
}

.total-credit-card {
  background: linear-gradient(135deg, var(--primary-light) 0%, #ffffff 100%);
}

.total-credit-card::before {
  background: linear-gradient(90deg, var(--primary), #0056b3);
}

.total-transfer {
  background: linear-gradient(135deg, var(--warning-light) 0%, #ffffff 100%);
}

.total-transfer::before {
  background: linear-gradient(90deg, var(--warning), #e0a800);
}

.total-all {
  background: linear-gradient(135deg, var(--info-light) 0%, #ffffff 100%);
}

.total-all::before {
  background: linear-gradient(90deg, var(--info), #138496);
}

.total-icon {
  width: 60px;
  height: 60px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 1rem;
  font-size: 1.5rem;
  color: white;
  background: linear-gradient(135deg, var(--primary), var(--info));
  box-shadow: var(--shadow-sm);
}

.total-cash .total-icon {
  background: linear-gradient(135deg, var(--success), #20c997);
}

.total-credit-card .total-icon {
  background: linear-gradient(135deg, var(--primary), #0056b3);
}

.total-transfer .total-icon {
  background: linear-gradient(135deg, var(--warning), #e0a800);
}

.total-all .total-icon {
  background: linear-gradient(135deg, var(--info), #138496);
}

.total-info h3 {
  margin: 0;
  font-size: 1.75rem;
  font-weight: 700;
  color: var(--text-primary);
  line-height: 1.2;
}

.total-info p {
  margin: 0;
  font-size: 0.875rem;
  color: var(--text-secondary);
  font-weight: 500;
}

@keyframes slideIn {
  from {
    opacity: 0;
    transform: translateX(-30px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

/* Chart Container */
.chart-container {
  position: relative;
  height: 300px;
  padding: 1rem;
}

/* Card Styles */
.card {
  border: 1px solid var(--border-color);
  border-radius: var(--border-radius-lg);
  box-shadow: var(--shadow-sm);
  transition: all 0.3s ease;
  background: white;
}

.card:hover {
  box-shadow: var(--shadow);
}

.card-header {
  background: linear-gradient(135deg, var(--light) 0%, #ffffff 100%);
  border-bottom: 1px solid var(--border-color);
  padding: 1rem 1.5rem;
  border-radius: var(--border-radius-lg) var(--border-radius-lg) 0 0;
  font-weight: 600;
  color: var(--text-primary);
}

.card-body {
  padding: 1.5rem;
}

/* Filter Styles */
.form-label {
  font-weight: 600;
  color: var(--text-primary);
  margin-bottom: 0.5rem;
}

.form-control {
  border: 1px solid var(--border-color);
  border-radius: var(--border-radius);
  padding: 0.75rem;
  transition: all 0.3s ease;
  background: white;
}

.form-control:focus {
  border-color: var(--primary);
  box-shadow: 0 0 0 0.2rem var(--primary-light);
  outline: 0;
}

.input-group-text {
  background: var(--light);
  border: 1px solid var(--border-color);
  color: var(--text-secondary);
}

/* Active Filters */
.active-filters {
  margin-bottom: 1rem;
}

.filter-badge {
  display: inline-flex;
  align-items: center;
  background: var(--primary-light);
  color: var(--primary);
  padding: 0.5rem 0.75rem;
  border-radius: var(--border-radius);
  font-size: 0.875rem;
  font-weight: 500;
  border: 1px solid var(--primary);
}

.filter-badge .btn-close {
  margin-left: 0.5rem;
  font-size: 0.75rem;
}

/* Modern Table */
.modern-table {
  width: 100%;
  border-collapse: collapse;
  background: white;
  border-radius: var(--border-radius-lg);
  overflow: hidden;
  box-shadow: var(--shadow-sm);
}

.modern-table thead th {
  background: linear-gradient(135deg, var(--light) 0%, #ffffff 100%);
  color: var(--text-primary);
  font-weight: 600;
  padding: 1rem;
  text-align: left;
  border-bottom: 2px solid var(--border-color);
  font-size: 0.875rem;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.modern-table tbody tr {
  border-bottom: 1px solid var(--border-color);
  transition: all 0.3s ease;
  animation: fadeIn 0.5s ease-out;
}

.modern-table tbody tr:hover {
  background: var(--light);
  transform: scale(1.01);
  box-shadow: var(--shadow-sm);
}

.modern-table tbody td {
  padding: 1rem;
  vertical-align: middle;
  color: var(--text-primary);
}

/* Avatar */
.modern-avatar {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-weight: 600;
  font-size: 0.875rem;
  box-shadow: var(--shadow-sm);
  background: linear-gradient(135deg, var(--primary), var(--info));
}

/* Badges */
.modern-badge {
  display: inline-block;
  padding: 0.375rem 0.75rem;
  font-size: 0.75rem;
  font-weight: 600;
  line-height: 1;
  text-align: center;
  white-space: nowrap;
  vertical-align: baseline;
  border-radius: var(--border-radius);
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.modern-badge-success {
  color: var(--success);
  background-color: var(--success-light);
  border: 1px solid var(--success);
}

.modern-badge-primary {
  color: var(--primary);
  background-color: var(--primary-light);
  border: 1px solid var(--primary);
}

.modern-badge-warning {
  color: var(--warning);
  background-color: var(--warning-light);
  border: 1px solid var(--warning);
}

.modern-badge-info {
  color: var(--info);
  background-color: var(--info-light);
  border: 1px solid var(--info);
}

/* Buttons */
.modern-btn {
  display: inline-block;
  font-weight: 600;
  text-align: center;
  vertical-align: middle;
  user-select: none;
  border: 1px solid transparent;
  padding: 0.5rem 1rem;
  font-size: 0.875rem;
  line-height: 1.5;
  border-radius: var(--border-radius);
  transition: all 0.3s ease;
  cursor: pointer;
  text-decoration: none;
}

.modern-btn-sm {
  padding: 0.375rem 0.75rem;
  font-size: 0.75rem;
  border-radius: var(--border-radius);
}

.modern-btn-outline-primary {
  color: var(--primary);
  border-color: var(--primary);
  background: transparent;
}

.modern-btn-outline-primary:hover {
  color: white;
  background-color: var(--primary);
  border-color: var(--primary);
  transform: translateY(-1px);
  box-shadow: var(--shadow-sm);
}

/* Empty State */
.empty-state {
  text-align: center;
  padding: 3rem 1rem;
  color: var(--text-secondary);
}

.empty-state i {
  margin-bottom: 1rem;
  opacity: 0.5;
}

.empty-state h5 {
  color: var(--text-primary);
  margin-bottom: 0.5rem;
}

.empty-state p {
  margin-bottom: 1.5rem;
}

/* Pagination */
.modern-pagination {
  display: flex;
  padding-left: 0;
  list-style: none;
  border-radius: var(--border-radius);
  margin: 0;
}

.modern-page-item {
  margin: 0 2px;
}

.modern-page-item.disabled .modern-page-link {
  color: var(--text-secondary);
  pointer-events: none;
  cursor: auto;
  background-color: var(--light);
  border-color: var(--border-color);
}

.modern-page-item.active .modern-page-link {
  z-index: 3;
  color: white;
  background-color: var(--primary);
  border-color: var(--primary);
  box-shadow: var(--shadow-sm);
}

.modern-page-link {
  position: relative;
  display: block;
  padding: 0.5rem 0.75rem;
  margin-left: -1px;
  line-height: 1.25;
  color: var(--primary);
  text-decoration: none;
  background-color: white;
  border: 1px solid var(--border-color);
  border-radius: var(--border-radius);
  transition: all 0.3s ease;
}

.modern-page-link:hover {
  z-index: 2;
  color: var(--primary);
  text-decoration: none;
  background-color: var(--light);
  border-color: var(--border-color);
  transform: translateY(-1px);
  box-shadow: var(--shadow-sm);
}

/* Responsive Design */
@media (max-width: 768px) {
  .container-fluid {
    padding: 1rem;
  }

  .total-card {
    margin-bottom: 1rem;
  }

  .total-info h3 {
    font-size: 1.5rem;
  }

  .modern-table {
    font-size: 0.875rem;
  }

  .modern-table thead th,
  .modern-table tbody td {
    padding: 0.75rem 0.5rem;
  }

  .modern-avatar {
    width: 32px;
    height: 32px;
    font-size: 0.75rem;
  }

  .chart-container {
    height: 250px;
  }
}

@media (max-width: 576px) {
  .total-info h3 {
    font-size: 1.25rem;
  }

  .total-info p {
    font-size: 0.75rem;
  }

  .modern-table {
    font-size: 0.75rem;
  }

  .chart-container {
    height: 200px;
  }
}