import {
  Animation<PERSON>urves,
  AnimationDurations,
  DateAdapter,
  MAT_DATE_FORMATS,
  MAT_DATE_LOCALE,
  MAT_DATE_LOCALE_FACTORY,
  MAT_NATIVE_DATE_FORMATS,
  MatLine,
  MatLineModule,
  MatNativeDateModule,
  NativeDateAdapter,
  NativeDateModule,
  VERSION,
  provideNativeDateAdapter,
  setLines
} from "./chunk-4D63OFOE.js";
import {
  ErrorStateMatcher,
  ShowOnDirtyErrorStateMatcher,
  _ErrorStateTracker
} from "./chunk-CFIWWTE3.js";
import {
  _MatInternalFormField
} from "./chunk-T3FVHANY.js";
import {
  MatRippleLoader
} from "./chunk-LGAH5QM7.js";
import {
  MAT_OPTGROUP,
  MAT_OPTION_PARENT_COMPONENT,
  MatOptgroup,
  MatOption,
  MatOptionModule,
  MatOptionSelectionChange,
  Mat<PERSON>eu<PERSON>Checkbox,
  Mat<PERSON>eu<PERSON>CheckboxModule,
  _countGroup<PERSON>abelsBeforeOption,
  _getOptionScrollPosition
} from "./chunk-P5DM45TM.js";
import {
  MatRippleModule
} from "./chunk-ODMW7XKU.js";
import {
  MAT_RIPPLE_GLOBAL_OPTIONS,
  MatRipple,
  RippleRef,
  RippleRenderer,
  RippleState,
  defaultRippleAnimationConfig
} from "./chunk-U6GPE26K.js";
import "./chunk-IDNV3ES2.js";
import {
  _StructuralStylesLoader
} from "./chunk-VQTRJ4G6.js";
import "./chunk-SZS4RJEH.js";
import "./chunk-F5YF3NDX.js";
import "./chunk-OXBCLJLK.js";
import {
  MATERIAL_SANITY_CHECKS,
  MatCommonModule
} from "./chunk-GAAQAWMG.js";
import "./chunk-OGBLP7U4.js";
import "./chunk-COKKOXWR.js";
import "./chunk-Y3P5KD7I.js";
import "./chunk-WPM5VTLQ.js";
import "./chunk-PEBH6BBU.js";
import "./chunk-4S3KYZTJ.js";
import "./chunk-V4F5PRXT.js";
export {
  AnimationCurves,
  AnimationDurations,
  DateAdapter,
  ErrorStateMatcher,
  MATERIAL_SANITY_CHECKS,
  MAT_DATE_FORMATS,
  MAT_DATE_LOCALE,
  MAT_DATE_LOCALE_FACTORY,
  MAT_NATIVE_DATE_FORMATS,
  MAT_OPTGROUP,
  MAT_OPTION_PARENT_COMPONENT,
  MAT_RIPPLE_GLOBAL_OPTIONS,
  MatCommonModule,
  MatLine,
  MatLineModule,
  MatNativeDateModule,
  MatOptgroup,
  MatOption,
  MatOptionModule,
  MatOptionSelectionChange,
  MatPseudoCheckbox,
  MatPseudoCheckboxModule,
  MatRipple,
  MatRippleLoader,
  MatRippleModule,
  NativeDateAdapter,
  NativeDateModule,
  RippleRef,
  RippleRenderer,
  RippleState,
  ShowOnDirtyErrorStateMatcher,
  VERSION,
  _ErrorStateTracker,
  _MatInternalFormField,
  _StructuralStylesLoader,
  _countGroupLabelsBeforeOption,
  _getOptionScrollPosition,
  defaultRippleAnimationConfig,
  provideNativeDateAdapter,
  setLines
};
